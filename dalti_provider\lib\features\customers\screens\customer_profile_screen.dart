import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../generated/l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/widgets/icon_container.dart';
import '../../../core/widgets/main_layout.dart';
import '../models/customer_models.dart';
import '../providers/customer_provider.dart';

/// Customer profile screen showing detailed customer information
class CustomerProfileScreen extends ConsumerStatefulWidget {
  final String customerId;

  const CustomerProfileScreen({super.key, required this.customerId});

  @override
  ConsumerState<CustomerProfileScreen> createState() =>
      _CustomerProfileScreenState();
}

class _CustomerProfileScreenState extends ConsumerState<CustomerProfileScreen> {
  @override
  void initState() {
    super.initState();

    // Load customer data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(singleCustomerNotifierProvider(widget.customerId).notifier)
          .loadCustomer();
      ref
          .read(
            customerAppointmentsNotifierProvider(widget.customerId).notifier,
          )
          .loadAppointments();
    });
  }

  void _onEditCustomer() {
    context.push('/customers/${widget.customerId}/edit');
  }

  void _onDeleteCustomer() {
    final customer = ref.read(
      singleCustomerNotifierProvider(widget.customerId),
    );
    if (customer == null) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Customer'),
            content: Text(
              'Are you sure you want to delete ${customer.firstName} ${customer.lastName}? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _deleteCustomer(customer);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteCustomer(Customer customer) async {
    try {
      final repository = ref.read(customerRepositoryProvider);
      await repository.deleteCustomer(customer.id);

      // Remove from customer list
      ref.read(customerNotifierProvider.notifier).removeCustomer(customer.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Customer ${customer.firstName} ${customer.lastName} deleted successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
        context.pop(); // Go back to customer list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete customer: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onAddAppointment() {
    // TODO: Navigate to add appointment screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add appointment feature coming soon')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customer = ref.watch(
      singleCustomerNotifierProvider(widget.customerId),
    );
    final appointments = ref.watch(
      customerAppointmentsNotifierProvider(widget.customerId),
    );

    if (customer == null) {
      return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          elevation: 0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          title: Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 12),
                child: IconContainer.header(
                  icon: Icons.arrow_back,
                  onTap: () => context.pop(),
                ),
              ),
              const Text('Customer Profile'),
            ],
          ),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: RTLAppBarBuilder.build(
        context: context,
        leading: IconContainer.header(
          icon: Icons.arrow_back,
          onTap: () => context.pop(),
        ),
        title: Text('${customer.firstName} ${customer.lastName}'),
        actions: [
          IconContainer.header(icon: Icons.edit, onTap: _onEditCustomer),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Customer Information Card (includes avatar and all info)
              _buildCustomerInfoCard(customer),

              const SizedBox(height: 16),

              // Quick Actions Card
              _buildQuickActionsCard(customer),

              const SizedBox(height: 16),

              // Customer Statistics Card
              _buildCustomerStatsCard(customer),

              const SizedBox(height: 16),

              // Recent Appointments Card
              _buildRecentAppointmentsCard(customer, appointments),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _onAddAppointment,
        child: const Icon(Icons.event_note),
      ),
    );
  }

  Widget _buildCustomerInfoCard(Customer customer) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header (without icon)
            Text(
              AppLocalizations.of(context).information,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 16),

            // Essential Customer Information
            if (customer.email != null)
              _buildInfoRow(
                icon: Icons.email,
                label: AppLocalizations.of(context).email,
                value: customer.email!,
              ),
            if (customer.phoneNumber != null)
              _buildInfoRow(
                icon: Icons.phone,
                label: AppLocalizations.of(context).mobile,
                value: customer.phoneNumber!,
              ),

            // Status
            _buildInfoRow(
              icon: Icons.info_outline,
              label: AppLocalizations.of(context).status,
              value: _getStatusLabel(customer.status),
            ),

            // Customer Since
            _buildInfoRow(
              icon: Icons.person_outline,
              label: AppLocalizations.of(context).customerSince,
              value: DateFormat('MMM d, yyyy').format(customer.createdAt),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onRefresh() async {
    await ref
        .read(singleCustomerNotifierProvider(widget.customerId).notifier)
        .loadCustomer();
    await ref
        .read(customerAppointmentsNotifierProvider(widget.customerId).notifier)
        .loadAppointments();
  }

  Widget _buildQuickActionsCard(Customer customer) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).quickActions,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 16),

            // Action Buttons Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  icon: Icons.phone,
                  label: AppLocalizations.of(context).call,
                  onTap: () => _onCallCustomer(),
                  enabled: customer.phoneNumber != null,
                ),
                _buildActionButton(
                  icon: Icons.email,
                  label: AppLocalizations.of(context).email,
                  onTap: () => _onEmailCustomer(),
                  enabled: customer.email != null,
                ),
                _buildActionButton(
                  icon: Icons.message,
                  label: AppLocalizations.of(context).sms,
                  onTap: () => _onSMSCustomer(),
                  enabled: customer.phoneNumber != null,
                ),
                _buildActionButton(
                  icon: Icons.event,
                  label: AppLocalizations.of(context).book,
                  onTap: () => _onAddAppointment(),
                  enabled: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool enabled,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: InkWell(
          onTap: enabled ? onTap : null,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color:
                  enabled
                      ? Theme.of(
                        context,
                      ).colorScheme.primary.withValues(alpha: 0.1)
                      : Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Icon(
                  icon,
                  color:
                      enabled
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color:
                        enabled
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  value,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerStatsCard(Customer customer) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).customerStatistics,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 16),

            // Stats Grid
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.event,
                    label: AppLocalizations.of(context).appointments,
                    value: customer.totalAppointments.toString(),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.attach_money,
                    label: AppLocalizations.of(context).totalSpent,
                    value: _formatCurrency(customer.totalSpent),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.schedule,
                    label: AppLocalizations.of(context).lastVisit,
                    value:
                        customer.lastAppointmentDate != null
                            ? DateFormat(
                              'MMM d',
                            ).format(customer.lastAppointmentDate!)
                            : AppLocalizations.of(context).never,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.person_outline,
                    label: 'Customer Since',
                    value: DateFormat('MMM yyyy').format(customer.createdAt),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: Theme.of(context).colorScheme.primary, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentAppointmentsCard(Customer customer, dynamic appointments) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).recentAppointments,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to full appointments list
                  },
                  child: Text(
                    AppLocalizations.of(context).viewAll,
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Appointments placeholder
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.event_available,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    AppLocalizations.of(context).noRecentAppointments,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  void _onCallCustomer() async {
    final customer = ref.read(
      singleCustomerNotifierProvider(widget.customerId),
    );
    if (customer?.phoneNumber != null) {
      final uri = Uri(scheme: 'tel', path: customer!.phoneNumber);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    }
  }

  void _onEmailCustomer() async {
    final customer = ref.read(
      singleCustomerNotifierProvider(widget.customerId),
    );
    if (customer?.email != null) {
      final uri = Uri(scheme: 'mailto', path: customer!.email);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    }
  }

  void _onSMSCustomer() async {
    final customer = ref.read(
      singleCustomerNotifierProvider(widget.customerId),
    );
    if (customer?.phoneNumber != null) {
      final uri = Uri(scheme: 'sms', path: customer!.phoneNumber);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    }
  }

  String _getStatusLabel(CustomerStatus status) {
    final l10n = AppLocalizations.of(context);
    switch (status) {
      case CustomerStatus.active:
        return l10n.activeCustomer;
      case CustomerStatus.inactive:
        return l10n.inactiveCustomer;
      case CustomerStatus.blocked:
        return l10n.blockedCustomer;
    }
  }
}
