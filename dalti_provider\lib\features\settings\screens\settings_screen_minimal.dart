import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/widgets/rtl_layout_components.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/theme_provider.dart';
import '../../../core/localization/language_provider.dart';
import '../../../core/localization/widgets/language_selector.dart';
import '../../../core/widgets/icon_container.dart';
import '../../../features/auth/providers/auth_provider.dart';
import '../../../generated/l10n/app_localizations.dart';

/// Minimal Settings Screen with clean, simple design
class MinimalSettingsScreen extends ConsumerWidget {
  const MinimalSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final themeNotifier = ref.read(themeNotifierProvider.notifier);
    final themeState = ref.watch(themeNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Row(
          children: [
            Padding(
              padding: const EdgeInsets.only(right: 12),
              child: IconContainer.header(
                icon: Icons.arrow_back,
                onTap: () => context.go('/dashboard'),
              ),
            ),
            Expanded(
              child: Text(
                l10n.settings,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          const SizedBox(height: 8),

          // Management Section
          _buildSectionHeader(context, l10n.management),
          _buildMinimalTile(
            context: context,
            icon: Icons.room_service,
            title: l10n.services,
            onTap: () => context.push('/services'),
          ),
          _buildMinimalTile(
            context: context,
            icon: Icons.location_on,
            title: l10n.locations,
            onTap: () => context.push('/locations'),
          ),
          _buildMinimalTile(
            context: context,
            icon: Icons.queue,
            title: l10n.queues,
            onTap: () => context.push('/queues'),
          ),

          const SizedBox(height: 32),

          // App Settings Section
          _buildSectionHeader(context, l10n.appSettings),
          _buildThemeMinimalTile(context, ref, themeNotifier, themeState),
          _buildLanguageMinimalTile(context, ref, l10n),
          _buildMinimalTile(
            context: context,
            icon: Icons.help_outline,
            title: l10n.helpAndSupport,
            onTap: () => context.push('/help'),
          ),

          const SizedBox(height: 32),

          // Account Section
          _buildSectionHeader(context, l10n.account),
          _buildMinimalTile(
            context: context,
            icon: Icons.logout,
            title: l10n.logout,
            onTap: () => _showLogoutDialog(context, ref, l10n),
            textColor: Theme.of(context).colorScheme.error,
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 8, top: 8),
      child: Text(
        title.toUpperCase(),
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildMinimalTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
    Color? textColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(
          icon,
          color: textColor ?? Theme.of(context).colorScheme.onSurface,
          size: 22,
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: textColor,
            fontWeight: FontWeight.w400,
          ),
        ),
        trailing:
            trailing ??
            Icon(
              RTLNavigation.getChevronIcon(context, isNext: true),
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              size: 20,
            ),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildThemeMinimalTile(
    BuildContext context,
    WidgetRef ref,
    ThemeNotifier themeNotifier,
    ThemeState themeState,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(
          themeState.themeMode == AppThemeMode.dark
              ? Icons.light_mode
              : Icons.dark_mode,
          color: Theme.of(context).colorScheme.onSurface,
          size: 22,
        ),
        title: Text(
          AppLocalizations.of(context).theme,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
        trailing: Transform.scale(
          scale: 0.8,
          child: Switch(
            value: themeState.themeMode == AppThemeMode.dark,
            onChanged: (value) => themeNotifier.toggleTheme(),
            activeColor: Theme.of(context).colorScheme.primary,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildLanguageMinimalTile(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
  ) {
    final languageState = ref.watch(languageProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(
          Icons.language,
          color: Theme.of(context).colorScheme.onSurface,
          size: 22,
        ),
        title: Text(
          l10n.language,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
        trailing:
            languageState.isChangingLanguage
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      languageState.currentLanguage.nativeName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      RTLNavigation.getChevronIcon(context, isNext: true),
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ],
                ),
        onTap:
            languageState.isChangingLanguage
                ? null
                : () => _showLanguageSelection(context),
        contentPadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showLanguageSelection(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.6,
            minChildSize: 0.4,
            maxChildSize: 0.8,
            expand: false,
            builder:
                (context, scrollController) => Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      Text(
                        AppLocalizations.of(context).selectLanguage,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 16),
                      const Expanded(child: LanguageSelector()),
                    ],
                  ),
                ),
          ),
    );
  }

  void _showLogoutDialog(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.logoutConfirmTitle),
            content: Text(l10n.logoutConfirmMessage),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(l10n.cancel),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref.read(authNotifierProvider.notifier).logout();
                  context.go('/login');
                },
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: Text(l10n.logout),
              ),
            ],
          ),
    );
  }
}
