import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../theme/theme_provider.dart';
import '../../features/dashboard/providers/profile_completion_provider.dart';
import '../../features/messages/providers/message_provider.dart';
import '../../features/notifications/providers/notifications_provider.dart';

import '../routing/app_routes.dart';
import '../../generated/l10n/app_localizations.dart';

import 'icon_container.dart';
import 'rtl_layout_components.dart';
import '../../features/appointments/providers/calendar_config_provider.dart';
import '../../features/appointments/widgets/calendar_config_dialog.dart';

/// RTL-aware app bar builder utility
class RTLAppBarBuilder {
  /// Creates an RTL-aware app bar with proper spacing and title overflow handling
  static PreferredSizeWidget build({
    required BuildContext context,
    required Widget leading,
    required Widget title,
    List<Widget> actions = const [],
    Color? backgroundColor,
  }) {
    return AppBar(
      automaticallyImplyLeading: false,
      elevation: 0,
      backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.surface,
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                leading,
                const SizedBox(width: 12), // RTL-aware spacing
                Expanded(child: _wrapTitleWithOverflow(title)),
              ],
            ),
          ),
          if (actions.isNotEmpty)
            Row(
              mainAxisSize: MainAxisSize.min,
              children: _addSpacingBetweenActions(actions),
            ),
        ],
      ),
    );
  }

  /// Wraps the title widget with overflow handling
  static Widget _wrapTitleWithOverflow(Widget title) {
    // If the title is already a Text widget, wrap it with overflow handling
    if (title is Text) {
      return Text(
        title.data ?? '',
        style: title.style,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      );
    }

    // For other widgets (like Row with badges), wrap in a flexible container
    // This ensures the title can shrink when needed
    return Flexible(child: title);
  }

  /// Adds proper spacing between action buttons
  static List<Widget> _addSpacingBetweenActions(List<Widget> actions) {
    if (actions.isEmpty) return actions;

    final spacedActions = <Widget>[];
    for (int i = 0; i < actions.length; i++) {
      spacedActions.add(actions[i]);
      // Add spacing between buttons (except after the last one)
      if (i < actions.length - 1) {
        spacedActions.add(const SizedBox(width: 8));
      }
    }

    return spacedActions;
  }
}

/// Calendar action types
enum CalendarAction { goToToday, showSettings }

/// Provider for triggering calendar actions from global AppBar
final calendarActionProvider = StateProvider<CalendarAction?>((ref) => null);

/// Appointments list action types
enum AppointmentsListAction { toggleFilters }

/// Provider for triggering appointments list actions from global AppBar
final appointmentsListActionProvider = StateProvider<AppointmentsListAction?>(
  (ref) => null,
);

/// Main layout with bottom navigation and user avatar header
class MainLayout extends ConsumerStatefulWidget {
  final Widget child;
  final String currentPath;

  const MainLayout({super.key, required this.child, required this.currentPath});

  @override
  ConsumerState<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends ConsumerState<MainLayout> {
  int _selectedIndex = 0;

  // Bottom navigation items - will be built dynamically with localized labels
  List<BottomNavigationBarItem> _getBottomNavItems(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return [
      BottomNavigationBarItem(
        icon: const Icon(Icons.dashboard),
        label: l10n.dashboard,
      ),
      BottomNavigationBarItem(
        icon: const Icon(Icons.calendar_today),
        label: l10n.calendar,
      ),
      BottomNavigationBarItem(
        icon: const Icon(Icons.qr_code_scanner, color: Colors.transparent),
        label: '',
      ), // Placeholder for QR scanner
      BottomNavigationBarItem(
        icon: const Icon(Icons.people),
        label: l10n.customers,
      ),
      BottomNavigationBarItem(
        icon: const Icon(Icons.event),
        label: l10n.appointments,
      ),
    ];
  }

  // Routes corresponding to bottom nav items (with QR scanner placeholder)
  final List<String> _routes = [
    '/dashboard',
    '/appointments',
    '', // QR scanner - handled separately
    '/customers',
    '/appointments-list',
  ];

  @override
  void initState() {
    super.initState();
    _updateSelectedIndex();
  }

  @override
  void didUpdateWidget(MainLayout oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentPath != widget.currentPath) {
      _updateSelectedIndex();
      if (widget.currentPath == AppRoutes.dashboard) {
        ref.read(profileCompletionNotifierProvider.notifier).refresh();
      }
    }
  }

  void _updateSelectedIndex() {
    final index = _routes.indexOf(widget.currentPath);
    if (index != -1) {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  void _onBottomNavTap(int index) {
    // Handle QR scanner button (index 2)
    if (index == 2) {
      _openQRScanner();
      return;
    }

    final route = _routes[index];

    if (route.isNotEmpty && index != _selectedIndex) {
      context.go(route);
    }
  }

  void _openQRScanner() {
    // Navigate to QR scanner screen
    context.push('/qr-scanner');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      appBar: _buildAppBar(),
      body: widget.child,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: _buildQRScannerFAB(),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return RTLAppBarBuilder.build(
      context: context,
      leading: _buildLeadingIcon(),
      title: _buildAppBarTitle(),
      actions: _buildAppBarActions(),
    );
  }

  Widget _buildLeadingIcon() {
    // For specific screens, show back button instead of profile icon
    if (_shouldShowBackButton()) {
      return IconContainer.header(
        icon: Icons.arrow_back,
        onTap: () => context.go('/dashboard'),
      );
    }

    // Default: show profile icon
    return IconContainer.header(
      icon: Icons.person,
      onTap: () => context.push('/profile'),
    );
  }

  bool _shouldShowBackButton() {
    return [
      '/appointments',
      '/appointments-list',
      '/customers',
      '/notifications',
    ].contains(widget.currentPath);
  }

  Widget _buildAppBarTitle() {
    if (widget.currentPath == '/messages') {
      return Consumer(
        builder: (context, ref, child) {
          final l10n = AppLocalizations.of(context);
          final unreadCount = ref.watch(unreadMessagesCountNotifierProvider);
          return Row(
            children: [
              Flexible(
                child: Text(
                  l10n.messages,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
              if (unreadCount > 0) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: context.colors.error,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    unreadCount.toString(),
                    style: context.textTheme.labelSmall?.copyWith(
                      color: context.colors.onError,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          );
        },
      );
    }
    return Text(_getAppBarTitle());
  }

  List<Widget> _buildAppBarActions() {
    switch (widget.currentPath) {
      case '/appointments':
        return _buildCalendarActions();
      case '/appointments-list':
        return _buildAppointmentsListActions();
      case '/customers':
        return _buildCustomersActions();
      case '/notifications':
        return _buildNotificationsActions();
      default:
        return _buildDefaultActions();
    }
  }

  List<Widget> _buildCalendarActions() {
    return [
      IconContainer.header(icon: Icons.today, onTap: () => _goToToday()),
      IconContainer.header(
        icon: Icons.settings,
        onTap: () => _showCalendarSettings(),
      ),
      IconContainer.header(
        icon: Icons.add,
        onTap: () => context.push('/appointments/add'),
      ),
    ];
  }

  List<Widget> _buildCustomersActions() {
    return [
      IconContainer.header(
        icon: Icons.person_add,
        onTap: () => context.push('/customers/new'),
      ),
    ];
  }

  List<Widget> _buildNotificationsActions() {
    return [
      Consumer(
        builder: (context, ref, child) {
          final unreadCount = ref.watch(unreadNotificationsCountProvider);
          if (unreadCount > 0) {
            return IconContainer.header(
              icon: Icons.done_all,
              onTap: () => _markAllNotificationsAsRead(),
            );
          }
          return const SizedBox.shrink();
        },
      ),
      IconContainer.header(
        icon: Icons.visibility_off, // Will be dynamic based on filter state
        onTap: () => _toggleNotificationFilter(),
      ),
    ];
  }

  List<Widget> _buildAppointmentsListActions() {
    return [
      IconContainer.header(
        icon: Icons.filter_list,
        onTap: () {
          ref.read(appointmentsListActionProvider.notifier).state =
              AppointmentsListAction.toggleFilters;
        },
      ),
      IconContainer.header(
        icon: Icons.add,
        onTap: () => context.push('/appointments/add'),
      ),
    ];
  }

  List<Widget> _buildDefaultActions() {
    return [
      Consumer(
        builder: (context, ref, child) {
          final unreadCount = ref.watch(unreadNotificationsCountProvider);
          return IconContainer.header(
            icon: Icons.notifications,
            onTap: () => context.push(AppRoutes.notifications),
            badge: unreadCount > 0 ? IconBadge.count(count: unreadCount) : null,
          );
        },
      ),
      IconContainer.header(
        icon: Icons.settings,
        onTap: () => context.push('/settings'),
      ),
    ];
  }

  // Action methods for screen-specific functionality
  void _goToToday() {
    // Trigger calendar action via provider
    ref.read(calendarActionProvider.notifier).state = CalendarAction.goToToday;
    // Reset after a short delay to allow calendar to respond
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        ref.read(calendarActionProvider.notifier).state = null;
      }
    });
  }

  void _showCalendarSettings() {
    // Show calendar settings dialog directly
    final calendarConfig = ref.read(calendarConfigProvider);
    showCalendarConfigDialog(
      context: context,
      currentTimeSlotInterval: calendarConfig.timeSlotInterval,
      onTimeSlotIntervalChanged: (interval) {
        ref.read(calendarConfigProvider.notifier).setTimeSlotInterval(interval);
      },
    );
  }

  void _markAllNotificationsAsRead() {
    ref.read(notificationsProvider.notifier).markAllAsRead();
  }

  void _toggleNotificationFilter() {
    // This will need to communicate with the NotificationsScreen
    // For now, show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Toggle notification filter'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  String _getAppBarTitle() {
    final l10n = AppLocalizations.of(context);
    switch (widget.currentPath) {
      case '/dashboard':
        return l10n.dashboard;
      case '/appointments':
        return l10n.calendar;
      case '/appointments-list':
        return l10n.appointmentsList;
      case '/customers':
        return l10n.customers;
      case '/messages':
        return l10n.messages;
      case '/notifications':
        return l10n.notifications;
      default:
        return l10n.daltiProvider;
    }
  }

  Widget _buildBottomNavigationBar() {
    // Remove the QR scanner placeholder item for the actual bottom nav
    final bottomNavItems = _getBottomNavItems(context);
    final navItems = [
      bottomNavItems[0], // Dashboard
      bottomNavItems[1], // Calendar
      bottomNavItems[3], // Customers
      bottomNavItems[4], // Appointments
    ];

    return BottomAppBar(
      elevation: 0,
      shape: const CircularNotchedRectangle(),
      notchMargin: 6,
      clipBehavior: Clip.antiAlias,
      child: SizedBox(
        height: 60,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(0, navItems[0]),
            _buildNavItem(1, navItems[1]),
            const SizedBox(width: 40), // Space for FAB
            _buildNavItem(3, navItems[2]), // Customers (index 3 in original)
            _buildNavItem(4, navItems[3]), // Appointments (index 4 in original)
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, BottomNavigationBarItem item) {
    final isSelected = _selectedIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () => _onBottomNavTap(index),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              item.icon is Icon ? (item.icon as Icon).icon : Icons.help,
              color:
                  isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 4),
            Text(
              item.label ?? '',
              style: TextStyle(
                fontSize: 12,
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRScannerFAB() {
    return FloatingActionButton(
      onPressed: _openQRScanner,
      backgroundColor: Theme.of(context).colorScheme.primary,
      shape: const CircleBorder(),
      elevation:
          0, // Very low elevation so SnackBars (elevation 6.0) appear above
      child: const Icon(Icons.qr_code_scanner, color: Colors.white, size: 28),
    );
  }
}

/// Helper function to determine if a route should use the main layout
bool shouldUseMainLayout(String path) {
  const mainLayoutRoutes = [
    '/dashboard',
    '/appointments',
    '/customers',
    '/analytics',
  ];

  return mainLayoutRoutes.contains(path);
}

/// Helper function to determine if a route should show back button
bool shouldShowBackButton(String path) {
  const noBackButtonRoutes = [
    '/dashboard',
    '/appointments',
    '/customers',
    '/analytics',
  ];

  return !noBackButtonRoutes.contains(path);
}
