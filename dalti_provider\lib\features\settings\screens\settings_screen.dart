import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/theme_provider.dart';
import '../../../core/localization/language_provider.dart';
import '../../../core/localization/widgets/language_selector.dart';
import '../../../core/widgets/icon_container.dart';
import '../../../features/auth/providers/auth_provider.dart';
import '../../../generated/l10n/app_localizations.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final themeNotifier = ref.read(themeNotifierProvider.notifier);
    final themeState = ref.watch(themeNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Row(
          children: [
            Padding(
              padding: const EdgeInsets.only(right: 12),
              child: IconContainer.header(
                icon: Icons.arrow_back,
                onTap: () => context.go('/dashboard'),
              ),
            ),
            Expanded(
              child: Text(
                l10n.settings,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Management Section
          _buildSectionHeader(context, l10n.management),
          _buildSettingsTile(
            context: context,
            icon: Icons.room_service,
            title: l10n.services,
            onTap: () => context.push('/services'),
          ),
          _buildSettingsTile(
            context: context,
            icon: Icons.location_on,
            title: l10n.locations,
            onTap: () => context.push('/locations'),
          ),
          _buildSettingsTile(
            context: context,
            icon: Icons.queue,
            title: l10n.queues,
            onTap: () => context.push('/queues'),
          ),

          const SizedBox(height: 24),

          // App Settings Section
          _buildSectionHeader(context, l10n.appSettings),
          _buildThemeSettingsTile(
            context,
            ref,
            l10n,
            themeNotifier,
            themeState,
          ),
          _buildLanguageSettingsTile(context, ref, l10n),
          _buildSettingsTile(
            context: context,
            icon: Icons.help,
            title: l10n.helpAndSupport,
            onTap: () => context.push('/help'),
          ),

          const SizedBox(height: 24),

          // Account Section
          _buildSectionHeader(context, l10n.account),
          _buildSettingsTile(
            context: context,
            icon: Icons.logout,
            title: l10n.logout,
            onTap: () => _showLogoutDialog(context, ref, l10n),
            textColor: context.colors.error,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 16, 0, 8),
      child: Text(
        title,
        style: context.textTheme.titleSmall?.copyWith(
          color: context.colors.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _getLocalizedThemeModeName(
    AppLocalizations l10n,
    AppThemeMode themeMode,
  ) {
    switch (themeMode) {
      case AppThemeMode.light:
        return l10n.themeModeLight;
      case AppThemeMode.dark:
        return l10n.themeModeDark;
      case AppThemeMode.system:
        return l10n.themeModeSystem;
    }
  }

  Widget _buildSettingsTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: textColor),
        title: Text(title, style: TextStyle(color: textColor)),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildThemeSettingsTile(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
    ThemeNotifier themeNotifier,
    ThemeState themeState,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          themeState.themeMode == AppThemeMode.dark
              ? Icons.light_mode
              : Icons.dark_mode,
        ),
        title: Text(l10n.theme),
        subtitle: Text(_getLocalizedThemeModeName(l10n, themeState.themeMode)),
        trailing: Transform.scale(
          scale: 0.8,
          child: Switch(
            value: themeState.themeMode == AppThemeMode.dark,
            onChanged: (value) => themeNotifier.toggleTheme(),
            activeColor: Theme.of(context).colorScheme.primary,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildLanguageSettingsTile(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
  ) {
    final languageState = ref.watch(languageProvider);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const Icon(Icons.language),
        title: Text(l10n.language),
        subtitle: Text(languageState.currentLanguage.nativeName),
        trailing:
            languageState.isChangingLanguage
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                : const Icon(Icons.chevron_right),
        onTap:
            languageState.isChangingLanguage
                ? null
                : () => _showLanguageSelection(context),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showLanguageSelection(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.6,
            minChildSize: 0.4,
            maxChildSize: 0.8,
            expand: false,
            builder:
                (context, scrollController) => Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      Text(
                        AppLocalizations.of(context).selectLanguage,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 16),
                      const Expanded(child: LanguageSelector()),
                    ],
                  ),
                ),
          ),
    );
  }

  void _showLogoutDialog(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.logoutConfirmTitle),
            content: Text(l10n.logoutConfirmMessage),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(l10n.cancel),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref.read(authNotifierProvider.notifier).logout();
                  context.go('/login');
                },
                style: TextButton.styleFrom(
                  foregroundColor: context.colors.error,
                ),
                child: Text(l10n.logout),
              ),
            ],
          ),
    );
  }
}
