import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../providers/queue_provider.dart';
import '../models/queue_models.dart';
import '../../../core/errors/api_exceptions.dart';
import '../../locations/providers/location_provider.dart';
import '../../locations/models/location_models.dart';
import '../../services/providers/service_provider.dart';
import '../../services/models/service_models.dart';
import '../../onboarding/widgets/opening_hours_widget.dart';
import '../../../core/widgets/icon_container.dart';
import '../../../core/theme/theme_provider.dart';

class QueueFormScreen extends ConsumerStatefulWidget {
  final int? queueId; // null for add, non-null for edit
  final int? initialLocationId; // for pre-selecting location when adding

  const QueueFormScreen({super.key, this.queueId, this.initialLocationId});

  @override
  ConsumerState<QueueFormScreen> createState() => _QueueFormScreenState();
}

class _QueueFormScreenState extends ConsumerState<QueueFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();

  int? _selectedLocationId;
  List<int> _selectedServiceIds = [];
  bool _isLoading = false;

  Map<String, List<TimeSlot>> _openingHours = {};

  @override
  void initState() {
    super.initState();

    // Set initial location if provided
    _selectedLocationId = widget.initialLocationId;

    // Initialize default opening hours
    _openingHours = _getDefaultOpeningHours();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(locationNotifierProvider.notifier).loadLocations();
      // Load services using the same pattern as Services screen
      _loadServicesForForm();

      if (widget.queueId != null) {
        _loadExistingQueue();
      }
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingQueue() async {
    try {
      setState(() => _isLoading = true);

      final queueNotifier = ref.read(queueNotifierProvider.notifier);
      final queue = queueNotifier.getQueueById(widget.queueId!);

      if (queue != null) {
        print('[QueueFormScreen] Loading existing queue: ${queue.id}');
        print(
          '[QueueFormScreen] Queue has opening hours: ${queue.openingHours != null}',
        );

        final openingHoursWidget = queue.getOpeningHoursForWidget();
        print(
          '[QueueFormScreen] Converted opening hours for widget: ${openingHoursWidget.keys.toList()}',
        );

        setState(() {
          _selectedLocationId = queue.locationId;
          _selectedServiceIds = queue.serviceIds;
          _titleController.text = queue.title;
          // Populate opening hours from queue data
          _openingHours = openingHoursWidget;
          _isLoading = false;
        });

        print(
          '[QueueFormScreen] State updated with opening hours: ${_openingHours.keys.toList()}',
        );
      } else {
        // Queue not found in current state, try to load it
        await queueNotifier.loadQueues(forceRefresh: true);
        final refreshedQueue = queueNotifier.getQueueById(widget.queueId!);

        if (refreshedQueue != null) {
          print(
            '[QueueFormScreen] Loading refreshed queue: ${refreshedQueue.id}',
          );
          print(
            '[QueueFormScreen] Refreshed queue has opening hours: ${refreshedQueue.openingHours != null}',
          );

          final refreshedOpeningHours =
              refreshedQueue.getOpeningHoursForWidget();
          print(
            '[QueueFormScreen] Converted refreshed opening hours: ${refreshedOpeningHours.keys.toList()}',
          );

          setState(() {
            _selectedLocationId = refreshedQueue.locationId;
            _selectedServiceIds = refreshedQueue.serviceIds;
            _titleController.text = refreshedQueue.title;
            // Populate opening hours from refreshed queue data
            _openingHours = refreshedOpeningHours;
            _isLoading = false;
          });

          print(
            '[QueueFormScreen] State updated with refreshed opening hours: ${_openingHours.keys.toList()}',
          );
        } else {
          throw Exception('Queue not found');
        }
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading queue: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final locationState = ref.watch(locationNotifierProvider);
    final serviceState = ref.watch(serviceNotifierProvider);
    final isEditing = widget.queueId != null;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Row(
          children: [
            IconContainer.header(
              icon: Icons.arrow_back,
              onTap: () => context.pop(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                isEditing
                    ? AppLocalizations.of(context).editQueue
                    : AppLocalizations.of(context).createQueue,
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
        actions: [
          IconContainer.header(icon: Icons.refresh, onTap: _refreshServices),
          const SizedBox(width: 8),
          if (isEditing) ...[
            IconContainer.header(
              icon: Icons.delete_outline,
              onTap: _deleteQueue,
            ),
            const SizedBox(width: 8),
          ],
          IconContainer.header(
            icon: Icons.check,
            onTap: _canSave() ? _saveQueue : null,
          ),
          const SizedBox(width: 16),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Queue Information Card
                    Card(
                      elevation: 0,
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Form header
                              Text(
                                AppLocalizations.of(context).queueInformation,
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 16),

                              // Queue title
                              TextFormField(
                                controller: _titleController,
                                decoration: InputDecoration(
                                  labelText:
                                      '${AppLocalizations.of(context).queueName} *',
                                  filled: true,
                                  fillColor: Colors.transparent,
                                  hintText:
                                      AppLocalizations.of(
                                        context,
                                      ).queueNameHint,
                                  border: const OutlineInputBorder(),
                                  prefixIcon: Icon(
                                    Icons.queue,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return AppLocalizations.of(
                                      context,
                                    ).queueNameRequired;
                                  }
                                  if (value.trim().length < 2) {
                                    return AppLocalizations.of(
                                      context,
                                    ).queueNameMinLength;
                                  }
                                  if (value.trim().length > 100) {
                                    return AppLocalizations.of(
                                      context,
                                    ).queueNameMaxLength;
                                  }
                                  return null;
                                },
                                textCapitalization: TextCapitalization.words,
                              ),
                              const SizedBox(height: 16),

                              // Location selection
                              _buildLocationDropdown(locationState.locations),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Service selection card
                    Card(
                      elevation: 0,
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: _buildServiceSelection(
                          serviceState.services,
                          serviceState,
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Opening Hours Section
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section header
                        Text(
                          AppLocalizations.of(context).queueOperatingHours,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          AppLocalizations.of(
                            context,
                          ).setQueueAvailabilityDescription,
                          style: context.textTheme.bodyMedium?.copyWith(
                            color: context.colors.onSurface.withValues(
                              alpha: 0.6,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        OpeningHoursWidget(
                          initialData: _openingHours,
                          onChanged: (openingHours) {
                            setState(() {
                              _openingHours = openingHours;
                            });
                          },
                          showHeader: false,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildLocationDropdown(List<Location> locations) {
    return DropdownButtonFormField<int>(
      value: _selectedLocationId,
      decoration: InputDecoration(
        labelText: AppLocalizations.of(context).locations,
        border: const OutlineInputBorder(),
        prefixIcon: const Icon(Icons.location_on),
      ),
      items:
          locations.map((location) {
            return DropdownMenuItem<int>(
              value: location.id,
              child: Text(location.name),
            );
          }).toList(),
      onChanged:
          widget.queueId != null
              ? null
              : (value) {
                // Disable for editing
                setState(() {
                  _selectedLocationId = value;
                });
              },
      validator: (value) {
        if (value == null) {
          return AppLocalizations.of(context).pleaseSelectLocation;
        }
        return null;
      },
    );
  }

  Widget _buildServiceSelection(
    List<Service> services,
    ServiceData serviceState,
  ) {
    // Filter services to show only active ones (defaults to true if not specified by API)
    final availableServices = services.where((s) => s.isServiceActive).toList();

    print(
      '[QueueFormScreen] Total services: ${services.length}, Active services: ${availableServices.length}',
    );
    print(
      '[QueueFormScreen] Service details: ${services.map((s) => 'ID:${s.id}, Title:${s.title}, isActive:${s.isActive}, isServiceActive:${s.isServiceActive}').join(', ')}',
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Text(
          AppLocalizations.of(context).queueServices,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context).selectServicesDescription,
          style: context.textTheme.bodyMedium?.copyWith(
            color: context.colors.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 16),

        // Show loading indicator if services are being loaded
        if (serviceState.isLoading && serviceState.services.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: context.colors.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: context.colors.outline.withOpacity(0.2),
              ),
            ),
            child: Center(
              child: Column(
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 12),
                  Text(AppLocalizations.of(context).loadingServices),
                ],
              ),
            ),
          )
        // Show error state if there's an error
        else if (serviceState.hasError)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: context.colors.errorContainer.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: context.colors.error.withOpacity(0.3)),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: context.colors.error,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Error loading services',
                    style: context.textTheme.titleMedium?.copyWith(
                      color: context.colors.error,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    serviceState.error ?? 'Unknown error occurred',
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.colors.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _refreshServices,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.colors.error,
                      foregroundColor: context.colors.onError,
                    ),
                  ),
                ],
              ),
            ),
          )
        else if (availableServices.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: context.colors.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: context.colors.outline.withOpacity(0.2),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.business_center_outlined,
                  size: 64,
                  color: context.colors.onSurfaceVariant,
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context).noServicesAvailable,
                  style: context.textTheme.titleMedium?.copyWith(
                    color: context.colors.onSurfaceVariant,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  AppLocalizations.of(context).createServicesFirstMessage,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => context.push('/services'),
                  icon: const Icon(Icons.add),
                  label: Text(AppLocalizations.of(context).addServices),
                ),
              ],
            ),
          )
        else ...[
          // Services list
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: context.colors.outline.withOpacity(0.2),
              ),
            ),
            child: Column(
              children: [
                // Select all/none controls
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: context.colors.surfaceVariant.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        AppLocalizations.of(context).servicesSelected(
                          _selectedServiceIds.length,
                          availableServices.length,
                        ),
                        style: context.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            if (_selectedServiceIds.length ==
                                availableServices.length) {
                              _selectedServiceIds.clear();
                            } else {
                              _selectedServiceIds =
                                  availableServices.map((s) => s.id).toList();
                            }
                          });
                        },
                        child: Text(
                          _selectedServiceIds.length == availableServices.length
                              ? AppLocalizations.of(context).clearAll
                              : AppLocalizations.of(context).selectAll,
                        ),
                      ),
                    ],
                  ),
                ),

                // Service items
                ...availableServices.asMap().entries.map((entry) {
                  final index = entry.key;
                  final service = entry.value;
                  final isSelected = _selectedServiceIds.contains(service.id);
                  final isLast = index == availableServices.length - 1;

                  return Container(
                    decoration: BoxDecoration(
                      border:
                          isLast
                              ? null
                              : Border(
                                bottom: BorderSide(
                                  color: context.colors.outline.withOpacity(
                                    0.1,
                                  ),
                                  width: 1,
                                ),
                              ),
                    ),
                    child: CheckboxListTile(
                      value: isSelected,
                      onChanged: (bool? value) {
                        setState(() {
                          if (value == true) {
                            _selectedServiceIds.add(service.id);
                          } else {
                            _selectedServiceIds.remove(service.id);
                          }
                        });
                      },
                      title: Text(
                        service.title,
                        style: context.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            service.formattedDuration,
                            style: context.textTheme.bodySmall?.copyWith(
                              color: context.colors.onSurface.withOpacity(0.6),
                            ),
                          ),
                          Text(
                            '${service.pointsRequirements} ${AppLocalizations.of(context).credits} • \$${(service.price ?? 0.0).toStringAsFixed(2)}',
                            style: context.textTheme.bodySmall?.copyWith(
                              color: context.colors.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                  );
                }).toList(),
              ],
            ),
          ),

          // Validation message
          if (_selectedServiceIds.isEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 16,
                    color: context.colors.error,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    AppLocalizations.of(context).pleaseSelectAtLeastOneService,
                    style: context.textTheme.bodySmall?.copyWith(
                      color: context.colors.error,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ],
    );
  }

  Widget _buildValidationWarnings() {
    if (_selectedServiceIds.length > 10) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          border: Border.all(color: Colors.orange.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange.shade600),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'This queue has many services (${_selectedServiceIds.length}). Consider splitting into multiple queues for better organization.',
                style: TextStyle(color: Colors.orange.shade700),
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  /// Convert widget opening hours to API format
  List<Map<String, dynamic>> _convertOpeningHoursToApiFormat(
    Map<String, List<TimeSlot>> widgetData,
  ) {
    final List<Map<String, dynamic>> apiHours = [];

    for (final entry in widgetData.entries) {
      final dayOfWeek = entry.key;
      final timeSlots = entry.value;

      // Convert TimeSlot objects to API format
      final hours =
          timeSlots
              .map(
                (slot) => {
                  'timeFrom':
                      '${slot.from.hour.toString().padLeft(2, '0')}:${slot.from.minute.toString().padLeft(2, '0')}',
                  'timeTo':
                      '${slot.to.hour.toString().padLeft(2, '0')}:${slot.to.minute.toString().padLeft(2, '0')}',
                },
              )
              .toList();

      apiHours.add({
        'dayOfWeek': dayOfWeek,
        'isActive': timeSlots.isNotEmpty,
        'hours': hours,
      });
    }

    return apiHours;
  }

  Future<void> _saveQueue() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final queueNotifier = ref.read(queueNotifierProvider.notifier);

      if (widget.queueId != null) {
        // Update existing queue with opening hours
        final apiOpeningHours = _convertOpeningHoursToApiFormat(_openingHours);
        final currentQueue = queueNotifier.getQueueById(widget.queueId!);
        final request = UpdateQueueRequest(
          title: _titleController.text.trim(),
          serviceIds: _selectedServiceIds,
          openingHours: apiOpeningHours,
          isActive: currentQueue?.isActive,
        );

        await queueNotifier.updateQueue(widget.queueId!, request);

        // Refresh queues list
        await queueNotifier.loadQueues(forceRefresh: true);

        if (mounted) {
          context.pop(true); // Pass true to indicate refresh needed
        }
      } else {
        // Create new queue with opening hours
        final apiOpeningHours = _convertOpeningHoursToApiFormat(_openingHours);
        final request = CreateQueueRequest(
          title: _titleController.text.trim(),
          locationId: _selectedLocationId!,
          serviceIds: _selectedServiceIds,
          openingHours: apiOpeningHours,
        );

        await queueNotifier.createQueue(request);

        // Refresh queues list
        await queueNotifier.loadQueues(forceRefresh: true);

        if (mounted) {
          context.pop(true); // Pass true to indicate refresh needed
        }
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving queue: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteQueue() async {
    if (widget.queueId == null) return;

    final shouldDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Queue'),
            content: Text(
              'Are you sure you want to delete the queue "${_titleController.text}"?\n\nThis action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (shouldDelete == true) {
      setState(() => _isLoading = true);

      try {
        await ref
            .read(queueNotifierProvider.notifier)
            .deleteQueue(widget.queueId!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Queue deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          context.pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting queue: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Load services for the form (ensures services are available)
  Future<void> _loadServicesForForm() async {
    final serviceNotifier = ref.read(serviceNotifierProvider.notifier);
    final currentState = ref.read(serviceNotifierProvider);

    // If no services are loaded or there's an error, force a refresh
    if (currentState.services.isEmpty || currentState.hasError) {
      print(
        '[QueueFormScreen] No services loaded or error state, forcing refresh',
      );
      await serviceNotifier.refresh();
    } else {
      print(
        '[QueueFormScreen] Services already loaded (${currentState.services.length} services)',
      );
      // Just trigger a normal load to ensure fresh data if needed
      await serviceNotifier.loadServices();
    }
  }

  /// Refresh services (same pattern as Services screen)
  Future<void> _refreshServices() async {
    await ref.read(serviceNotifierProvider.notifier).refresh();
  }

  Map<String, List<TimeSlot>> _getDefaultOpeningHours() {
    final defaultSlot = TimeSlot(
      from: const TimeOfDay(hour: 9, minute: 0),
      to: const TimeOfDay(hour: 17, minute: 0),
    );

    return {
      'Monday': [defaultSlot],
      'Tuesday': [defaultSlot],
      'Wednesday': [defaultSlot],
      'Thursday': [defaultSlot],
      'Friday': [defaultSlot],
      'Saturday': [defaultSlot],
      'Sunday': [defaultSlot],
    };
  }

  bool _canSave() {
    return _titleController.text.trim().isNotEmpty &&
        _selectedLocationId != null &&
        _selectedServiceIds.isNotEmpty;
  }
}
