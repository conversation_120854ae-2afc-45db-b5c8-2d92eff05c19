import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/widgets/icon_container.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../models/appointment_models.dart';
import '../../queues/providers/queue_provider.dart';

/// Full-screen appointment filters modal
class AppointmentFiltersScreen extends ConsumerStatefulWidget {
  final AppointmentStatus? selectedStatus;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? selectedQueueId;
  final Function(AppointmentStatus?, DateTime?, DateTime?, String?)
  onFiltersApplied;

  const AppointmentFiltersScreen({
    super.key,
    this.selectedStatus,
    this.startDate,
    this.endDate,
    this.selectedQueueId,
    required this.onFiltersApplied,
  });

  @override
  ConsumerState<AppointmentFiltersScreen> createState() =>
      _AppointmentFiltersScreenState();
}

class _AppointmentFiltersScreenState
    extends ConsumerState<AppointmentFiltersScreen> {
  AppointmentStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedQueueId;

  @override
  void initState() {
    super.initState();
    // Initialize with current filter values
    _selectedStatus = widget.selectedStatus;
    _startDate = widget.startDate;
    _endDate = widget.endDate;
    _selectedQueueId = widget.selectedQueueId;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  IconContainer.header(
                    icon: Icons.arrow_back,
                    onTap: () => Navigator.of(context).pop(),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      l10n.filterAppointments,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ),
            Row(
              children: [
                // Clear filters button
                IconContainer.header(
                  icon: Icons.clear_all,
                  onTap: _clearAllFilters,
                ),
                const SizedBox(width: 8),
                // Apply filters button
                IconContainer.header(icon: Icons.check, onTap: _applyFilters),
              ],
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            _buildHeaderCard(),

            const SizedBox(height: 24),

            // Status Filter Card
            _buildStatusFilterCard(),

            const SizedBox(height: 24),

            // Date Range Filter Card
            _buildDateRangeFilterCard(),

            const SizedBox(height: 24),

            // Queue Filter Card
            _buildQueueFilterCard(),

            const SizedBox(height: 24),

            // Active Filters Summary
            _buildActiveFiltersSummary(),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.filter_list,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context).filterAppointments,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        AppLocalizations.of(context).customizeAppointmentView,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusFilterCard() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.label_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).statusFilter,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusChips(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChips() {
    final l10n = AppLocalizations.of(context);
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        // All status chip
        FilterChip(
          label: Text(AppLocalizations.of(context).all),
          selected: _selectedStatus == null,
          onSelected: (selected) {
            setState(() {
              _selectedStatus = selected ? null : _selectedStatus;
            });
          },
          backgroundColor: Theme.of(context).colorScheme.surface,
          selectedColor: Theme.of(
            context,
          ).colorScheme.primary.withValues(alpha: 0.1),
          checkmarkColor: Theme.of(context).colorScheme.primary,
          labelStyle: TextStyle(
            color:
                _selectedStatus == null
                    ? Theme.of(context).colorScheme.primary
                    : null,
            fontWeight: _selectedStatus == null ? FontWeight.w500 : null,
          ),
          side: BorderSide(
            color:
                _selectedStatus == null
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        // Individual status chips
        ...AppointmentStatus.values.map((status) {
          final isSelected = _selectedStatus == status;
          return FilterChip(
            label: Text(_getStatusDisplayName(status, l10n)),
            selected: isSelected,
            onSelected: (selected) {
              setState(() {
                _selectedStatus = selected ? status : null;
              });
            },
            backgroundColor: Theme.of(context).colorScheme.surface,
            selectedColor: _getStatusColor(status).withValues(alpha: 0.1),
            checkmarkColor: _getStatusColor(status),
            labelStyle: TextStyle(
              color: isSelected ? _getStatusColor(status) : null,
              fontWeight: isSelected ? FontWeight.w500 : null,
            ),
            side: BorderSide(
              color:
                  isSelected
                      ? _getStatusColor(status)
                      : Theme.of(
                        context,
                      ).colorScheme.outline.withValues(alpha: 0.3),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildDateRangeFilterCard() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.date_range,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Date Range',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildProfessionalDateRange(),
          ],
        ),
      ),
    );
  }

  Widget _buildQueueFilterCard() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.queue,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).queueFilter,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildQueueSelection(),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveFiltersSummary() {
    final l10n = AppLocalizations.of(context);
    final hasActiveFilters =
        _selectedStatus != null ||
        _startDate != null ||
        _endDate != null ||
        _selectedQueueId != null;

    if (!hasActiveFilters) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.filter_alt,
                  color: Theme.of(context).colorScheme.primary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Active Filters',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (_selectedStatus != null)
              Text(
                '${AppLocalizations.of(context).statusLabel}: ${_getStatusDisplayName(_selectedStatus!, l10n)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            if (_startDate != null || _endDate != null) ...[
              const SizedBox(height: 4),
              Text(
                '${AppLocalizations.of(context).dateRangeLabel}: ${_startDate != null ? '${_startDate!.day}/${_startDate!.month}/${_startDate!.year}' : AppLocalizations.of(context).any} - ${_endDate != null ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}' : AppLocalizations.of(context).any}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
            if (_selectedQueueId != null) ...[
              const SizedBox(height: 4),
              Text(
                '${AppLocalizations.of(context).queueLabel}: ${_getQueueName(_selectedQueueId!)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQueueSelection() {
    final queueState = ref.watch(queueNotifierProvider);

    if (queueState.isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (queueState.error != null) {
      return Text(
        '${AppLocalizations.of(context).errorLoadingQueues}: ${queueState.error}',
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: Colors.red),
      );
    }

    if (queueState.queues.isEmpty) {
      return Text(
        AppLocalizations.of(context).noQueuesFound,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // All queues option
        _buildQueueOption(
          id: null,
          name: AppLocalizations.of(context).allQueues,
          isSelected: _selectedQueueId == null,
          onTap: () {
            setState(() {
              _selectedQueueId = null;
            });
          },
        ),

        const SizedBox(height: 8),

        // Individual queue options
        ...queueState.queues.map(
          (queue) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildQueueOption(
              id: queue.id.toString(),
              name: queue.title,
              isSelected: _selectedQueueId == queue.id.toString(),
              onTap: () {
                setState(() {
                  _selectedQueueId = queue.id.toString();
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQueueOption({
    required String? id,
    required String name,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.05)
                  : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected
                    ? Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.3)
                    : Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.2),
            width: isSelected ? 1.5 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Theme.of(
                          context,
                        ).colorScheme.primary.withValues(alpha: 0.1)
                        : Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                id == null ? Icons.all_inclusive : Icons.queue,
                size: 20,
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                name,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color:
                      isSelected
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
          ],
        ),
      ),
    );
  }

  void _clearAllFilters() {
    setState(() {
      _selectedStatus = null;
      _startDate = null;
      _endDate = null;
      _selectedQueueId = null;
    });
  }

  Widget _buildProfessionalDateRange() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Quick date range chips
        Text(
          AppLocalizations.of(context).quickSelection,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildDateRangeChip(
              AppLocalizations.of(context).today,
              () => _setDateRange(DateTime.now(), DateTime.now()),
            ),
            _buildDateRangeChip(
              AppLocalizations.of(context).thisWeek,
              () => _setThisWeek(),
            ),
            _buildDateRangeChip(
              AppLocalizations.of(context).thisMonth,
              () => _setThisMonth(),
            ),
            if (_startDate != null || _endDate != null)
              _buildDateRangeChip(
                AppLocalizations.of(context).clear,
                () => _clearDateRange(),
                isDestructive: true,
              ),
          ],
        ),

        const SizedBox(height: 20),

        // Custom date selection
        Text(
          AppLocalizations.of(context).customRange,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 12),

        // Start Date
        _buildProfessionalDateField(
          label: AppLocalizations.of(context).startDate,
          date: _startDate,
          onTap: _selectStartDate,
          icon: Icons.calendar_today,
        ),

        const SizedBox(height: 12),

        // End Date
        _buildProfessionalDateField(
          label: AppLocalizations.of(context).endDate,
          date: _endDate,
          onTap: _selectEndDate,
          icon: Icons.event,
        ),
      ],
    );
  }

  Widget _buildDateRangeChip(
    String label,
    VoidCallback onPressed, {
    bool isDestructive = false,
  }) {
    return FilterChip(
      label: Text(label),
      selected: false,
      onSelected: (_) => onPressed(),
      backgroundColor: Theme.of(context).colorScheme.surface,
      selectedColor:
          isDestructive
              ? Colors.red.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
      labelStyle: TextStyle(
        color:
            isDestructive ? Colors.red : Theme.of(context).colorScheme.primary,
        fontWeight: FontWeight.w500,
      ),
      side: BorderSide(
        color:
            isDestructive
                ? Colors.red.withValues(alpha: 0.5)
                : Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    );
  }

  Widget _buildProfessionalDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    final hasValue = date != null;
    final l10n = AppLocalizations.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              hasValue
                  ? Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.05)
                  : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                hasValue
                    ? Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.3)
                    : Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.2),
            width: hasValue ? 1.5 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    hasValue
                        ? Theme.of(
                          context,
                        ).colorScheme.primary.withValues(alpha: 0.1)
                        : Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                size: 20,
                color:
                    hasValue
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    hasValue
                        ? '${date.day}/${date.month}/${date.year}'
                        : label == l10n.startDate
                        ? l10n.selectStartDate
                        : l10n.selectEndDate,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color:
                          hasValue
                              ? Theme.of(context).colorScheme.onSurface
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                      fontWeight: hasValue ? FontWeight.w500 : FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  void _setDateRange(DateTime start, DateTime end) {
    setState(() {
      _startDate = start;
      _endDate = end;
    });
  }

  void _setThisWeek() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    _setDateRange(startOfWeek, endOfWeek);
  }

  void _setThisMonth() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    _setDateRange(startOfMonth, endOfMonth);
  }

  void _clearDateRange() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  void _applyFilters() {
    widget.onFiltersApplied(
      _selectedStatus,
      _startDate,
      _endDate,
      _selectedQueueId,
    );
    Navigator.of(context).pop();
  }

  String _getStatusDisplayName(
    AppointmentStatus status,
    AppLocalizations l10n,
  ) {
    switch (status) {
      case AppointmentStatus.pending:
        return l10n.pending;
      case AppointmentStatus.scheduled:
        return l10n.scheduled;
      case AppointmentStatus.confirmed:
        return l10n.confirmed;
      case AppointmentStatus.inProgress:
        return l10n.inProgress;
      case AppointmentStatus.completed:
        return l10n.completed;
      case AppointmentStatus.canceled:
        return l10n.canceled;
      case AppointmentStatus.noShow:
        return l10n.noShow;
      case AppointmentStatus.rescheduled:
        return l10n.rescheduled;
    }
  }

  String _getQueueName(String queueId) {
    final queueState = ref.read(queueNotifierProvider);
    final queue = queueState.queues.firstWhere(
      (q) => q.id.toString() == queueId,
      orElse: () => throw StateError('Queue not found'),
    );
    return queue.title;
  }

  Color _getStatusColor(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Colors.orange;
      case AppointmentStatus.scheduled:
        return Colors.blue;
      case AppointmentStatus.confirmed:
        return Colors.blue;
      case AppointmentStatus.inProgress:
        return Colors.green;
      case AppointmentStatus.completed:
        return Colors.green;
      case AppointmentStatus.canceled:
        return Colors.red;
      case AppointmentStatus.noShow:
        return Colors.grey;
      case AppointmentStatus.rescheduled:
        return Colors.purple;
    }
  }
}

/// Show appointment filters screen
Future<void> showAppointmentFiltersScreen({
  required BuildContext context,
  AppointmentStatus? selectedStatus,
  DateTime? startDate,
  DateTime? endDate,
  String? selectedQueueId,
  required Function(AppointmentStatus?, DateTime?, DateTime?, String?)
  onFiltersApplied,
}) {
  return Navigator.of(context).push<void>(
    MaterialPageRoute(
      builder:
          (context) => AppointmentFiltersScreen(
            selectedStatus: selectedStatus,
            startDate: startDate,
            endDate: endDate,
            selectedQueueId: selectedQueueId,
            onFiltersApplied: onFiltersApplied,
          ),
      fullscreenDialog: true,
    ),
  );
}
